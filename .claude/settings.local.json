{"permissions": {"allow": ["Bash(npm create:*)", "Bash(npm install)", "Bash(npx:*)", "Bash(npm install:*)", "Bash(./node_modules/.bin/tailwindcss:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(npm run dev:*)", "Bash(rg:*)", "WebFetch(domain:ui.shadcn.com)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(pkill:*)", "Bash(sudo chown:*)", "Bash(npm cache clean:*)", "Bash(NPM_CONFIG_CACHE=/tmp/.npm npx shadcn@latest add dashboard-01)", "Bash(npm ls:*)", "<PERSON><PERSON>(qwen:*)", "Bash(export OPENAI_BASE_URL=\"https://dashscope.aliyuncs.com/compatible-mode/v1\")", "<PERSON><PERSON>(echo:*)", "Bash(find /Users/<USER>/leitond/gis-examples/src -name \"*.tsx\" -o -name \"*.ts\")", "Bash(find /Users/<USER>/leitond/gis-examples -name \"*.json\" -type f ! -path \"*/node_modules/*\")", "Bash(find /Users/<USER>/leitond/gis-examples -name \".env*\" -type f ! -path \"*/node_modules/*\")", "Bash(ls -la /Users/<USER>/leitond/gis-examples)", "Bash(npm outdated)", "<PERSON><PERSON>(touch:*)", "Bash(ccr:*)"], "deny": []}}
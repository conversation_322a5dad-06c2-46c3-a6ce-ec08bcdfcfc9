# ForgeCode MCP Integration

This project now includes Model Context Protocol (MCP) support for enhanced AI-assisted development.

## MCP Features

The MCP server provides the following tools:

### 1. Project Information Tool
- **Name**: `get_project_info`
- **Description**: Get comprehensive information about the current GIS project
- **Usage**: Provides project metadata, technologies used, and available features

### 2. GIS Data Analysis Tool
- **Name**: `analyze_gis_data`
- **Description**: Analyze various GIS data formats and structures
- **Parameters**: 
  - `dataType`: Type of GIS data (geojson, shapefile, etc.)
- **Usage**: Provides analysis recommendations and supported operations

### 3. Map Component Generator
- **Name**: `generate_map_component`
- **Description**: Generate React components for map visualization
- **Parameters**:
  - `mapType`: Type of map component (leaflet, mapbox, etc.)
  - `features`: Array of features to include
- **Usage**: Creates boilerplate React components for different map types

## Usage

### Starting the MCP Server
```bash
# Start the MCP server
npm run mcp:start

# Start in development mode with auto-reload
npm run mcp:dev
```

### Configuration
The MCP server is configured in `forge.yaml`:
```yaml
mcp:
  enabled: true
  servers:
    - name: "forgecode-mcp"
      command: "node"
      args: ["./mcp-server.js"]
      env:
        NODE_ENV: "development"
```

### Integration with Forge
When using Forge with this project, the MCP server will automatically provide:
- Context about the GIS project structure
- Tools for analyzing and working with geospatial data
- Component generation capabilities
- Project-specific recommendations

## Development

The MCP server is implemented in `mcp-server.js` and uses the official MCP SDK. It provides:
- Tool handlers for GIS-specific operations
- Resource handlers for project files and documentation
- Type-safe interfaces for all operations

## Dependencies

- `@modelcontextprotocol/sdk`: Core MCP functionality
- `nodemon`: Development auto-reload (dev dependency)

This integration enhances the development experience by providing AI assistants with deep context about the GIS project and specialized tools for working with geospatial data.
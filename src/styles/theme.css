:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(0 0% 20%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(0 0% 20%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(0 0% 20%);
  --primary: hsl(217.2193 91.2195% 59.8039%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(220.0000 14.2857% 95.8824%);
  --secondary-foreground: hsl(215 13.7931% 34.1176%);
  --muted: hsl(210 20.0000% 98.0392%);
  --muted-foreground: hsl(220 8.9362% 46.0784%);
  --accent: hsl(204.0000 93.7500% 93.7255%);
  --accent-foreground: hsl(224.4444 64.2857% 32.9412%);
  --destructive: hsl(0 84.2365% 60.1961%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(220 13.0435% 90.9804%);
  --input: hsl(220 13.0435% 90.9804%);
  --ring: hsl(217.2193 91.2195% 59.8039%);
  --chart-1: hsl(217.2193 91.2195% 59.8039%);
  --chart-2: hsl(221.2121 83.1933% 53.3333%);
  --chart-3: hsl(224.2781 76.3265% 48.0392%);
  --chart-4: hsl(225.9310 70.7317% 40.1961%);
  --chart-5: hsl(224.4444 64.2857% 32.9412%);
  --sidebar: hsl(210 20.0000% 98.0392%);
  --sidebar-foreground: hsl(0 0% 20%);
  --sidebar-primary: hsl(217.2193 91.2195% 59.8039%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(204.0000 93.7500% 93.7255%);
  --sidebar-accent-foreground: hsl(224.4444 64.2857% 32.9412%);
  --sidebar-border: hsl(220 13.0435% 90.9804%);
  --sidebar-ring: hsl(217.2193 91.2195% 59.8039%);
  --bim-viewer-bg: #f5f5f5;
  --bim-viewer-loading-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bim-viewer-error-bg: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  --bim-control-panel-bg: rgba(0,0,0,0.8);
  --bim-control-panel-text: white;
  --bim-button-primary: #007bff;
  --bim-button-success: #28a745;
  --bim-button-danger: #dc3545;
  --bim-progress-bg: rgba(255,255,255,0.3);
  --bim-progress-fill: white;
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: hsl(0 0% 9.0196%);
  --foreground: hsl(0 0% 89.8039%);
  --card: hsl(0 0% 14.9020%);
  --card-foreground: hsl(0 0% 89.8039%);
  --popover: hsl(0 0% 14.9020%);
  --popover-foreground: hsl(0 0% 89.8039%);
  --primary: hsl(217.2193 91.2195% 59.8039%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(0 0% 14.9020%);
  --secondary-foreground: hsl(0 0% 89.8039%);
  --muted: hsl(0 0% 14.9020%);
  --muted-foreground: hsl(0 0% 63.9216%);
  --accent: hsl(224.4444 64.2857% 32.9412%);
  --accent-foreground: hsl(213.3333 96.9231% 87.2549%);
  --destructive: hsl(0 84.2365% 60.1961%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(0 0% 25.0980%);
  --input: hsl(0 0% 25.0980%);
  --ring: hsl(217.2193 91.2195% 59.8039%);
  --chart-1: hsl(213.1169 93.9024% 67.8431%);
  --chart-2: hsl(217.2193 91.2195% 59.8039%);
  --chart-3: hsl(221.2121 83.1933% 53.3333%);
  --chart-4: hsl(224.2781 76.3265% 48.0392%);
  --chart-5: hsl(225.9310 70.7317% 40.1961%);
  --sidebar: hsl(0 0% 9.0196%);
  --sidebar-foreground: hsl(0 0% 89.8039%);
  --sidebar-primary: hsl(217.2193 91.2195% 59.8039%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(224.4444 64.2857% 32.9412%);
  --sidebar-accent-foreground: hsl(213.3333 96.9231% 87.2549%);
  --sidebar-border: hsl(0 0% 25.0980%);
  --sidebar-ring: hsl(217.2193 91.2195% 59.8039%);
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-bim-viewer-bg: var(--bim-viewer-bg);
  --color-bim-viewer-loading-bg: var(--bim-viewer-loading-bg);
  --color-bim-viewer-error-bg: var(--bim-viewer-error-bg);
  --color-bim-control-panel-bg: var(--bim-control-panel-bg);
  --color-bim-control-panel-text: var(--bim-control-panel-text);
  --color-bim-button-primary: var(--bim-button-primary);
  --color-bim-button-success: var(--bim-button-success);
  --color-bim-button-danger: var(--bim-button-danger);
  --color-bim-progress-bg: var(--bim-progress-bg);
  --color-bim-progress-fill: var(--bim-progress-fill);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}
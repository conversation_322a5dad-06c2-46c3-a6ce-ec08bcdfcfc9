import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { SidebarProvider, SidebarInset } from "./components/ui/sidebar"
import { AppSidebar, SiteHeader } from "./components"
import Home from "./pages/Home"
import Dashboard from "./pages/Dashboard"
import AddExtrude from "./pages/AddExtrude"
import NotFound from "./pages/NotFound"
import { TestButton } from "./TestButton"

export default function App() {
  return (
    <Router>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <SiteHeader />
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            <Routes>
              <Route path="/" element={<Navigate to="/home" replace />} />
              <Route path="/home" element={<Home />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/model_effects/add_extrude" element={<AddExtrude />} />
              <Route path="/test" element={<TestButton />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </Router>
  )
}

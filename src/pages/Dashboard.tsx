import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Badge } from "../components/ui/badge"
import { But<PERSON> } from "../components/ui/button"
import { Input } from "../components/ui/input"
import {
  BarChart3,
  Users,
  Activity,
  DollarSign,
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  Download,
  Filter,
  Search,
  MoreVertical,
  MapPin,
  Layers,
  Globe,
  Database,
  FileText,
  Building2,
  Eye,
  Plus
} from "lucide-react"
import { PageTransition, FadeIn, SlideIn, ScaleIn } from "../components/PageTransition"
import { useNavigate } from "react-router-dom"

export default function Dashboard() {
  const navigate = useNavigate()

  const handleMenuClick = (path: string) => {
    navigate(path)
  }

  return (
    <PageTransition>
      <div className="min-h-screen bg-white">
        {/* Dashboard Layout */}
        <div className="flex h-screen">
          {/* Left Sidebar */}
          <div className="w-64 border-r border-gray-200 bg-white">
            {/* Company Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-lg bg-gray-900 flex items-center justify-center">
                  <Building2 className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h1 className="font-semibold text-gray-900">GIS System</h1>
                </div>
              </div>
            </div>

            {/* Quick Create Button */}
            <div className="p-4">
              <Button className="w-full bg-gray-900 hover:bg-gray-800 text-white">
                <Plus className="w-4 h-4 mr-2" />
                快速创建
              </Button>
            </div>

            {/* Navigation */}
            <nav className="px-4">
              <div className="space-y-1">
                {navigationItems.map((item, index) => (
                  <div key={index} className="py-1">
                    <button 
                      onClick={() => item.path && handleMenuClick(item.path)}
                      className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors ${
                        item.active ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <item.icon className="w-4 h-4" />
                      <span className="text-sm font-medium">{item.name}</span>
                    </button>
                  </div>
                ))}
              </div>
              
              {/* Documents Section */}
              <div className="mt-8">
                <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wide">
                  Documents
                </h3>
                <div className="mt-2 space-y-1">
                  {documentsItems.map((item, index) => (
                    <button key={index} className="w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50 hover:text-gray-900">
                      <item.icon className="w-4 h-4" />
                      <span className="text-sm font-medium">{item.name}</span>
                    </button>
                  ))}
                </div>
              </div>
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Content Header */}
            <div className="px-8 py-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <FileText className="w-5 h-5 text-gray-400" />
                <h1 className="text-xl font-semibold text-gray-900">数据概览</h1>
              </div>
            </div>

            {/* Dashboard Content */}
            <div className="flex-1 p-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Revenue Card */}
                <FadeIn>
                  <Card className="p-6">
                    <CardHeader className="p-0 mb-4">
                      <div className="flex items-center justify-between">
                        <CardDescription className="text-sm text-gray-600">
                          总收入
                        </CardDescription>
                        <div className="flex items-center gap-2">
                          <TrendingUp className="w-4 h-4 text-green-500" />
                          <span className="text-sm font-medium text-green-500">+12.5%</span>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-0">
                      <div className="text-3xl font-bold text-gray-900 mb-2">
                        ¥1,250,000
                      </div>
                      <p className="text-sm text-gray-600">
                        本月持续增长
                      </p>
                    </CardContent>
                  </Card>
                </FadeIn>

                {/* Visitors Card */}
                <FadeIn delay={100}>
                  <Card className="p-6">
                    <CardHeader className="p-0 mb-4">
                      <div className="flex items-center justify-between">
                        <CardDescription className="text-sm text-gray-600">
                          总访问量
                        </CardDescription>
                        <div className="flex items-center gap-2">
                          <TrendingUp className="w-4 h-4 text-blue-500" />
                          <span className="text-sm font-medium text-blue-500">+8.2%</span>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-0">
                      <div className="text-3xl font-bold text-gray-900 mb-2">
                        45,231
                      </div>
                      <p className="text-sm text-gray-600">
                        过去3个月总计
                      </p>
                      <div className="mt-4 text-sm text-gray-600">
                        过去6个月的访问者数据
                      </div>
                    </CardContent>
                  </Card>
                </FadeIn>

                {/* GIS Stats Cards */}
                <FadeIn delay={200}>
                  <Card className="p-6">
                    <CardHeader className="p-0 mb-4">
                      <div className="flex items-center justify-between">
                        <CardDescription className="text-sm text-gray-600">
                          地图图层
                        </CardDescription>
                        <Layers className="w-4 h-4 text-purple-500" />
                      </div>
                    </CardHeader>
                    <CardContent className="p-0">
                      <div className="text-3xl font-bold text-gray-900 mb-2">
                        128
                      </div>
                      <p className="text-sm text-gray-600">
                        活跃图层数量
                      </p>
                    </CardContent>
                  </Card>
                </FadeIn>

                {/* Storage Card */}
                <FadeIn delay={300}>
                  <Card className="p-6">
                    <CardHeader className="p-0 mb-4">
                      <div className="flex items-center justify-between">
                        <CardDescription className="text-sm text-gray-600">
                          存储使用
                        </CardDescription>
                        <Database className="w-4 h-4 text-orange-500" />
                      </div>
                    </CardHeader>
                    <CardContent className="p-0">
                      <div className="text-3xl font-bold text-gray-900 mb-2">
                        68.5%
                      </div>
                      <p className="text-sm text-gray-600">
                        系统存储占用率
                      </p>
                    </CardContent>
                  </Card>
                </FadeIn>
              </div>

              {/* Recent Activity */}
              <FadeIn delay={400}>
                <div className="mt-8">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">最近活动</h2>
                  <Card>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {activities.map((activity, index) => (
                          <div key={index} className="flex items-start gap-3">
                            <div className={`p-2 rounded-full ${activity.iconBg}`}>
                              <activity.icon className={`w-4 h-4 ${activity.iconColor}`} />
                            </div>
                            <div className="flex-1">
                              <p className="text-sm font-medium text-gray-900">
                                {activity.title}
                              </p>
                              <p className="text-xs text-gray-500 mt-1">
                                {activity.time}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </FadeIn>
            </div>
          </div>
        </div>
      </div>
    </PageTransition>
  )
}

// Navigation data
const navigationItems = [
  {
    name: "仪表板",
    icon: BarChart3,
    active: true,
    path: "/dashboard"
  },
  {
    name: "地图视图",
    icon: Globe,
    active: false,
    path: "/map-view"
  },
  {
    name: "数据管理",
    icon: Database,
    active: false,
    path: "/data-management"
  },
  {
    name: "图层控制",
    icon: Layers,
    active: false,
    path: "/layer-control"
  },
  {
    name: "空间分析",
    icon: Activity,
    active: false,
    path: "/spatial-analysis"
  },
  {
    name: "项目管理",
    icon: MapPin,
    active: false,
    path: "/projects"
  },
  {
    name: "可视化",
    icon: Eye,
    active: false,
    path: "/visualization"
  },
  {
    name: "报表中心",
    icon: TrendingUp,
    active: false,
    path: "/reports"
  },
  {
    name: "团队协作",
    icon: Users,
    active: false,
    path: "/team"
  },
  {
    name: "系统设置",
    icon: Building2,
    active: false,
    path: "/settings"
  }
]

const documentsItems = [
  {
    name: "数据字典",
    icon: Database,
  },
  {
    name: "分析报告",
    icon: FileText,
  },
  {
    name: "地图图集",
    icon: Globe,
  },
  {
    name: "操作手册",
    icon: FileText,
  },
  {
    name: "API文档",
    icon: FileText,
  }
]

const statsCards = [
  {
    title: "总用户数",
    value: "2,543",
    percentage: "+12.5%",
    trend: "up",
    icon: Users,
    iconBg: "bg-blue-100",
    iconColor: "text-blue-600",
    bgPattern: "bg-blue-500"
  },
  {
    title: "地图访问量",
    value: "45,231",
    percentage: "+8.2%",
    trend: "up",
    icon: Globe,
    iconBg: "bg-green-100",
    iconColor: "text-green-600",
    bgPattern: "bg-green-500"
  },
  {
    title: "数据层数",
    value: "128",
    percentage: "-2.4%",
    trend: "down",
    icon: Layers,
    iconBg: "bg-purple-100",
    iconColor: "text-purple-600",
    bgPattern: "bg-purple-500"
  },
  {
    title: "存储使用率",
    value: "68.5%",
    percentage: "+5.3%",
    trend: "up",
    icon: Database,
    iconBg: "bg-orange-100",
    iconColor: "text-orange-600",
    bgPattern: "bg-orange-500"
  }
]

const activities = [
  {
    title: "新增地图图层",
    time: "2 分钟前",
    icon: Layers,
    iconBg: "bg-blue-100",
    iconColor: "text-blue-600"
  },
  {
    title: "数据导出完成",
    time: "15 分钟前",
    icon: Download,
    iconBg: "bg-green-100",
    iconColor: "text-green-600"
  },
  {
    title: "系统更新",
    time: "1 小时前",
    icon: Activity,
    iconBg: "bg-purple-100",
    iconColor: "text-purple-600"
  },
  {
    title: "用户注册",
    time: "3 小时前",
    icon: Users,
    iconBg: "bg-orange-100",
    iconColor: "text-orange-600"
  }
]

const dataItems = [
  {
    name: "城市边界数据",
    type: "矢量",
    points: "12,543",
    updated: "10 分钟前",
    status: "活跃"
  },
  {
    name: "卫星影像图",
    type: "栅格",
    points: "1,024,000",
    updated: "1 小时前",
    status: "活跃"
  },
  {
    name: "交通路网",
    type: "矢量",
    points: "45,678",
    updated: "2 天前",
    status: "活跃"
  },
  {
    name: "地形高程",
    type: "栅格",
    points: "2,048,000",
    updated: "1 周前",
    status: "暂停"
  }
]
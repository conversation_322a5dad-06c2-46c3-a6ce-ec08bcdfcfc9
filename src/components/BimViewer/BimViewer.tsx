import React, { useEffect, useRef, useId, useState } from 'react';
import type { BimViewerOptions, BimViewerInstance, BimModel } from '../../types/bim-air';

interface BimViewerProps {
  modelService?: string;
  fileService?: string;
  bimAirUrl?: string;
  models?: BimModel[];
  onViewerReady?: (viewer: BimViewerInstance) => void;
  onModelsLoaded?: (lightModels: any) => void;
  onError?: (error: string) => void;
  style?: React.CSSProperties;
  className?: string;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
}

const BimViewer: React.FC<BimViewerProps> = ({
  modelService = "https://static.belife-bim.com/modelApi",
  fileService = "https://static.belife-bim.com/fileApi",
  bimAirUrl = "/js",
  models = [],
  onViewerReady,
  onModelsLoaded,
  onError,
  style = { height: '100%', width: '100%' },
  className = '',
  loadingComponent,
  errorComponent,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const vueInstanceRef = useRef<any>(null);
  const viewerRef = useRef<BimViewerInstance | null>(null);
  const elementId = useId().replace(/:/g, ''); // 生成唯一ID并移除冒号
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 动态加载脚本
  const loadScript = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      // 检查脚本是否已经加载
      if (document.querySelector(`script[src="${src}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = src;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
      document.head.appendChild(script);
    });
  };

  // 动态加载样式
  const loadStylesheet = (href: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      // 检查样式是否已经加载
      if (document.querySelector(`link[href="${href}"]`)) {
        resolve();
        return;
      }

      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`Failed to load stylesheet: ${href}`));
      document.head.appendChild(link);
    });
  };

  // 初始化 BimAir
  const initializeBimAir = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 加载必要的资源
      await Promise.all([
        loadStylesheet('https://static.belife-bim.com/bimAir/BimAir.css'),
        loadScript('https://static.belife-bim.com/vue.min.js'),
        loadScript('https://static.belife-bim.com/bimAir/BimAir.umd.min.js'),
      ]);

      // 等待 Vue 和 BimAir 加载完成
      if (!window.Vue || !window.BimAir) {
        throw new Error('Vue or BimAir failed to load');
      }

      // 创建 Vue 实例
      if (containerRef.current && !vueInstanceRef.current) {
        // 创建 viewer-wrapper 元素
        const viewerWrapper = document.createElement('div');
        viewerWrapper.innerHTML = `<viewer-wrapper elementId="${elementId}" style="height: 100%; width: 100%"></viewer-wrapper>`;
        containerRef.current.appendChild(viewerWrapper);

        // 创建 Vue 实例
        vueInstanceRef.current = new window.Vue({
          el: viewerWrapper,
        });
      }

      // 加载 BimAir
      await window.BimAir.Loader({ url: bimAirUrl });

      // 创建 Viewer 实例
      const options: BimViewerOptions = {
        elementId,
        modelService,
        fileService,
      };

      const viewer = new window.BimAir.Viewer(options);
      viewerRef.current = viewer;

      // 通知父组件 viewer 已准备就绪
      onViewerReady?.(viewer);

      // 如果有模型需要加载，则加载模型
      if (models.length > 0) {
        try {
          const lightModels = await viewer.loadModels(models);
          onModelsLoaded?.(lightModels);
        } catch (modelError) {
          console.error('Failed to load models:', modelError);
          const errorMsg = `Failed to load models: ${modelError}`;
          setError(errorMsg);
          onError?.(errorMsg);
        }
      }

      setIsLoading(false);
    } catch (err) {
      console.error('Failed to initialize BimAir:', err);
      const errorMsg = `Failed to initialize BimAir: ${err}`;
      setError(errorMsg);
      onError?.(errorMsg);
      setIsLoading(false);
    }
  };

  // 清理函数
  const cleanup = () => {
    try {
      // 销毁 Viewer 实例
      if (viewerRef.current && typeof viewerRef.current.destroy === 'function') {
        viewerRef.current.destroy();
      }
      viewerRef.current = null;

      // 销毁 Vue 实例
      if (vueInstanceRef.current && typeof vueInstanceRef.current.$destroy === 'function') {
        vueInstanceRef.current.$destroy();
      }
      vueInstanceRef.current = null;

      // 清理 DOM
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }
    } catch (err) {
      console.error('Error during cleanup:', err);
    }
  };

  useEffect(() => {
    initializeBimAir();

    return cleanup;
  }, []);

  // 当模型列表变化时重新加载模型
  useEffect(() => {
    if (viewerRef.current && models.length > 0 && !isLoading) {
      viewerRef.current.loadModels(models)
        .then((lightModels) => {
          onModelsLoaded?.(lightModels);
        })
        .catch((err) => {
          console.error('Failed to load models:', err);
          const errorMsg = `Failed to load models: ${err}`;
          setError(errorMsg);
          onError?.(errorMsg);
        });
    }
  }, [models, isLoading]);

  const defaultLoadingComponent = (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      background: '#f5f5f5'
    }}>
      <div>Loading BimAir Viewer...</div>
    </div>
  );

  const defaultErrorComponent = (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      background: '#ffebee',
      color: '#c62828',
      padding: '20px',
      textAlign: 'center'
    }}>
      <div>
        <h3>Error</h3>
        <p>{error}</p>
      </div>
    </div>
  );

  return (
    <div 
      ref={containerRef}
      style={style}
      className={className}
    >
      {isLoading && (loadingComponent || defaultLoadingComponent)}
      {error && (errorComponent || defaultErrorComponent)}
    </div>
  );
};

export default BimViewer;
export type { BimViewerProps, BimViewerInstance };
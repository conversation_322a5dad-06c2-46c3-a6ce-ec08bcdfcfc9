// Mock data for BIM model component
export const mockStore = {
  viewer: null,
  isViewerReady: false,
  models: [],
  loadingProgress: 0,
  error: null
};

export const mockQuery = {
  availableModels: [
    { id: "626b4d1ebfe39e58ae7b66a2", version: 1, name: "Building Model A" },
    { id: "626b4d1ebfe39e58ae7b66a3", version: 2, name: "Building Model B" },
    { id: "626b4d1ebfe39e58ae7b66a4", version: 1, name: "Infrastructure Model" }
  ]
};

export const mockRootProps = {
  modelService: "https://static.belife-bim.com/modelApi" as const,
  fileService: "https://static.belife-bim.com/fileApi" as const,
  bimAirUrl: "/js" as const,
  initialModels: [
    { id: "626b4d1ebfe39e58ae7b66a2", version: 1 }
  ]
};
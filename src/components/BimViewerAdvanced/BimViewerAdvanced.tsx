import React, { useState, useCallback, useRef } from 'react';
import B<PERSON><PERSON>ie<PERSON> from '../BimViewer';
import type { BimViewerInstance, BimModel } from '../../types/bim-air';

interface BimViewerAdvancedProps {
  initialModels?: BimModel[];
  onViewerReady?: (viewer: BimViewerInstance) => void;
  onModelsLoaded?: (lightModels: any) => void;
  style?: React.CSSProperties;
  className?: string;
}

/**
 * 高级 BimViewer 组件，提供更多控制功能
 */
const BimViewerAdvanced: React.FC<BimViewerAdvancedProps> = ({
  initialModels = [],
  onViewerReady,
  onModelsLoaded,
  style,
  className,
}) => {
  const [viewer, setViewer] = useState<BimViewerInstance | null>(null);
  const [models, setModels] = useState<BimModel[]>(initialModels);
  const [isViewerReady, setIsViewerReady] = useState(false);
  const [isModelsLoaded, setIsModelsLoaded] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout>();

  const handleViewerReady = useCallback((viewerInstance: BimViewerInstance) => {
    console.log('BimAir Viewer is ready:', viewerInstance);
    setViewer(viewerInstance);
    setIsViewerReady(true);
    onViewerReady?.(viewerInstance);
  }, [onViewerReady]);

  const handleModelsLoaded = useCallback((lightModels: any) => {
    console.log('Models loaded:', lightModels);
    setIsModelsLoaded(true);
    setLoadingProgress(100);
    
    // 清除加载超时
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }
    
    onModelsLoaded?.(lightModels);
  }, [onModelsLoaded]);

  const handleError = useCallback((errorMsg: string) => {
    setError(errorMsg);
    setLoadingProgress(0);
    
    // 清除加载超时
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }
  }, []);

  const addModel = useCallback((model: BimModel) => {
    setModels(prev => [...prev, model]);
  }, []);

  const removeModel = useCallback((modelId: string) => {
    setModels(prev => prev.filter(model => model.id !== modelId));
  }, []);

  const clearModels = useCallback(() => {
    setModels([]);
    setIsModelsLoaded(false);
    setLoadingProgress(0);
  }, []);

  const loadModelsManually = useCallback(async (modelsToLoad: BimModel[]) => {
    if (!viewer) {
      console.warn('Viewer is not ready yet');
      return;
    }

    try {
      setLoadingProgress(10);
      setError(null);
      
      // 设置加载超时
      loadingTimeoutRef.current = setTimeout(() => {
        setError('模型加载超时，请检查网络连接');
        setLoadingProgress(0);
      }, 30000); // 30秒超时

      const lightModels = await viewer.loadModels(modelsToLoad);
      handleModelsLoaded(lightModels);
    } catch (err) {
      handleError(`手动加载模型失败: ${err}`);
    }
  }, [viewer, handleModelsLoaded, handleError]);

  // 自定义加载组件
  const customLoadingComponent = (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white'
    }}>
      <div style={{ marginBottom: '20px' }}>
        <div style={{
          width: '60px',
          height: '60px',
          border: '4px solid rgba(255,255,255,0.3)',
          borderTop: '4px solid white',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
      </div>
      <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '10px' }}>
        正在加载 BimAir 3D 查看器
      </div>
      <div style={{ fontSize: '14px', opacity: 0.8 }}>
        请稍候，正在初始化 3D 环境...
      </div>
      {loadingProgress > 0 && (
        <div style={{ marginTop: '20px', width: '200px' }}>
          <div style={{
            width: '100%',
            height: '4px',
            background: 'rgba(255,255,255,0.3)',
            borderRadius: '2px',
            overflow: 'hidden'
          }}>
            <div style={{
              width: `${loadingProgress}%`,
              height: '100%',
              background: 'white',
              transition: 'width 0.3s ease'
            }} />
          </div>
          <div style={{ textAlign: 'center', marginTop: '8px', fontSize: '12px' }}>
            {loadingProgress}%
          </div>
        </div>
      )}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );

  // 自定义错误组件
  const customErrorComponent = (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
      color: 'white',
      padding: '40px'
    }}>
      <div style={{ fontSize: '48px', marginBottom: '20px' }}>⚠️</div>
      <div style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '10px' }}>
        加载失败
      </div>
      <div style={{ fontSize: '14px', textAlign: 'center', marginBottom: '20px', opacity: 0.9 }}>
        {error}
      </div>
      <button
        onClick={() => window.location.reload()}
        style={{
          padding: '10px 20px',
          background: 'rgba(255,255,255,0.2)',
          border: '1px solid rgba(255,255,255,0.3)',
          borderRadius: '6px',
          color: 'white',
          cursor: 'pointer',
          fontSize: '14px'
        }}
      >
        重新加载页面
      </button>
    </div>
  );

  return (
    <div style={{ height: '100%', position: 'relative' }}>
      <BimViewer
        models={models}
        onViewerReady={handleViewerReady}
        onModelsLoaded={handleModelsLoaded}
        onError={handleError}
        style={style}
        className={className}
        loadingComponent={customLoadingComponent}
        errorComponent={customErrorComponent}
      />
      
      {/* 控制面板 */}
      {isViewerReady && (
        <div style={{
          position: 'absolute',
          top: '20px',
          right: '20px',
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '15px',
          borderRadius: '8px',
          minWidth: '250px',
          fontSize: '12px'
        }}>
          <h4 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>控制面板</h4>
          
          <div style={{ marginBottom: '10px' }}>
            <strong>状态:</strong>
            <div>Viewer: {isViewerReady ? '✅ 就绪' : '❌ 未就绪'}</div>
            <div>Models: {isModelsLoaded ? '✅ 已加载' : '❌ 未加载'}</div>
          </div>

          <div style={{ marginBottom: '10px' }}>
            <strong>当前模型 ({models.length}):</strong>
            {models.map((model, index) => (
              <div key={model.id} style={{ fontSize: '11px', opacity: 0.8 }}>
                {index + 1}. {model.id} (v{model.version})
              </div>
            ))}
          </div>

          <div style={{ display: 'flex', gap: '5px', flexWrap: 'wrap' }}>
            <button
              onClick={() => addModel({ id: `model-${Date.now()}`, version: 1 })}
              style={{
                padding: '4px 8px',
                background: '#007bff',
                border: 'none',
                borderRadius: '3px',
                color: 'white',
                fontSize: '11px',
                cursor: 'pointer'
              }}
            >
              添加模型
            </button>
            <button
              onClick={clearModels}
              style={{
                padding: '4px 8px',
                background: '#dc3545',
                border: 'none',
                borderRadius: '3px',
                color: 'white',
                fontSize: '11px',
                cursor: 'pointer'
              }}
            >
              清空模型
            </button>
            <button
              onClick={() => loadModelsManually([{ id: "626b4d1ebfe39e58ae7b66a2", version: 1 }])}
              style={{
                padding: '4px 8px',
                background: '#28a745',
                border: 'none',
                borderRadius: '3px',
                color: 'white',
                fontSize: '11px',
                cursor: 'pointer'
              }}
            >
              加载示例
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default BimViewerAdvanced;
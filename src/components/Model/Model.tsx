import React, { useRef, useEffect, useId, useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { ModelProps, ModelRef, ModelStatus, ModelLoadingState, VueComponentConfig } from './types';
import { useVueIntegration } from './hooks/useVueIntegration';
import { useBimAirIntegration } from './hooks/useBimAirIntegration';
import { BimViewerInstance } from '../../types/bim-air';

const Model = forwardRef<ModelRef, ModelProps>(({
  vueComponentName = 'viewer-wrapper',
  vueComponentProps = {},
  modelService = "https://static.belife-bim.com/modelApi",
  fileService = "https://static.belife-bim.com/fileApi",
  bimAirUrl = "/js",
  models = [],
  style = { height: '100%', width: '100%' },
  className = '',
  width,
  height,
  onViewerReady,
  onModelsLoaded,
  onError,
  onVueComponentReady,
  onVueComponentDestroyed,
  loadingComponent,
  errorComponent,
  enableAutoResize = true,
  enableErrorBoundary = true,
  retryAttempts = 3,
  retryDelay = 1000
}, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const elementId = useId().replace(/:/g, '');
  
  const [loadingState, setLoadingState] = useState<ModelLoadingState>({
    status: ModelStatus.IDLE,
    progress: 0,
    message: 'Initializing...'
  });
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Vue 组件配置
  const vueComponentConfig: VueComponentConfig = {
    name: vueComponentName,
    template: `<${vueComponentName} elementId="${elementId}" v-bind="$props" style="height: 100%; width: 100%"></${vueComponentName}>`,
    props: vueComponentProps,
    data: () => ({
      elementId,
      ...vueComponentProps
    }),
    mounted() {
      console.log(`Vue component ${vueComponentName} mounted with elementId:`, elementId);
    }
  };

  // Vue 集成 Hook
  const {
    createVueComponent,
    destroyVueComponent,
    updateVueProps,
    getVueInstance
  } = useVueIntegration({
    containerRef,
    componentConfig: vueComponentConfig,
    onReady: (vueInstance) => {
      console.log('Vue component ready:', vueInstance);
      onVueComponentReady?.(vueInstance);
    },
    onError: (err) => {
      setError(err);
      setLoadingState(prev => ({ ...prev, status: ModelStatus.ERROR }));
      onError?.(err);
    },
    onDestroyed: () => {
      console.log('Vue component destroyed');
      onVueComponentDestroyed?.();
    }
  });

  // BimAir 集成 Hook
  const {
    initializeBimAir,
    loadModels: loadBimModels,
    destroyViewer,
    getViewer,
    isLoading: isBimAirLoading,
    isReady: isBimAirReady
  } = useBimAirIntegration({
    elementId,
    modelService,
    fileService,
    bimAirUrl,
    onViewerReady: (viewer) => {
      console.log('BimAir viewer ready:', viewer);
      setLoadingState({
        status: ModelStatus.READY,
        progress: 100,
        message: 'Ready'
      });
      onViewerReady?.(viewer);
    },
    onModelsLoaded: (lightModels) => {
      console.log('Models loaded:', lightModels);
      onModelsLoaded?.(lightModels);
    },
    onError: (err) => {
      setError(err);
      setLoadingState(prev => ({ ...prev, status: ModelStatus.ERROR }));
      onError?.(err);
    }
  });

  // 初始化组件
  const initializeComponent = useCallback(async () => {
    try {
      setLoadingState({
        status: ModelStatus.LOADING,
        progress: 10,
        message: 'Loading Vue component...'
      });
      setError(null);

      // 创建 Vue 组件
      await createVueComponent();
      
      setLoadingState(prev => ({
        ...prev,
        progress: 50,
        message: 'Initializing BimAir...'
      }));

      // 初始化 BimAir
      await initializeBimAir();

      // 如果有模型需要加载
      if (models.length > 0 && isBimAirReady) {
        setLoadingState(prev => ({
          ...prev,
          progress: 80,
          message: 'Loading models...'
        }));
        await loadBimModels(models);
      }

    } catch (err) {
      console.error('Failed to initialize component:', err);
      const errorMsg = `Initialization failed: ${err}`;
      setError(errorMsg);
      setLoadingState({
        status: ModelStatus.ERROR,
        progress: 0,
        message: errorMsg
      });
    }
  }, [createVueComponent, initializeBimAir, models, isBimAirReady, loadBimModels]);

  // 重试机制
  const handleRetry = useCallback(async () => {
    if (retryCount < retryAttempts) {
      setRetryCount(prev => prev + 1);
      setTimeout(() => {
        initializeComponent();
      }, retryDelay);
    }
  }, [retryCount, retryAttempts, retryDelay, initializeComponent]);

  // 清理函数
  const cleanup = useCallback(() => {
    destroyViewer();
    destroyVueComponent();
    setLoadingState({
      status: ModelStatus.DESTROYED,
      progress: 0,
      message: 'Destroyed'
    });
  }, [destroyViewer, destroyVueComponent]);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    getVueInstance,
    getViewerInstance: getViewer,
    reload: initializeComponent,
    destroy: cleanup,
    updateProps: updateVueProps
  }), [getVueInstance, getViewer, initializeComponent, cleanup, updateVueProps]);

  // 组件挂载时初始化
  useEffect(() => {
    initializeComponent();
    return cleanup;
  }, []);

  // 监听模型变化
  useEffect(() => {
    if (isBimAirReady && models.length > 0) {
      loadBimModels(models);
    }
  }, [models, isBimAirReady, loadBimModels]);

  // 计算样式
  const containerStyle: React.CSSProperties = {
    ...style,
    ...(width && { width }),
    ...(height && { height })
  };

  // 默认加载组件
  const defaultLoadingComponent = (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      background: '#f5f5f5'
    }}>
      <div style={{ marginBottom: '10px' }}>
        {loadingState.message}
      </div>
      <div style={{
        width: '200px',
        height: '4px',
        background: '#e0e0e0',
        borderRadius: '2px',
        overflow: 'hidden'
      }}>
        <div style={{
          width: `${loadingState.progress}%`,
          height: '100%',
          background: '#1976d2',
          transition: 'width 0.3s ease'
        }} />
      </div>
    </div>
  );

  // 默认错误组件
  const defaultErrorComponent = (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      background: '#ffebee',
      color: '#c62828',
      padding: '20px',
      textAlign: 'center'
    }}>
      <h3>Error</h3>
      <p>{error}</p>
      {retryCount < retryAttempts && (
        <button 
          onClick={handleRetry}
          style={{
            marginTop: '10px',
            padding: '8px 16px',
            background: '#1976d2',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Retry ({retryCount + 1}/{retryAttempts})
        </button>
      )}
    </div>
  );

  return (
    <div 
      ref={containerRef}
      style={containerStyle}
      className={className}
    >
      {loadingState.status === ModelStatus.LOADING && (loadingComponent || defaultLoadingComponent)}
      {loadingState.status === ModelStatus.ERROR && (errorComponent || defaultErrorComponent)}
    </div>
  );
});

Model.displayName = 'Model';

export default Model;
export type { ModelProps, ModelRef };

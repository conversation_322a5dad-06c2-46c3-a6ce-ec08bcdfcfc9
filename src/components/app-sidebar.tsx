import * as React from "react"
import {
  IconCamera,
  IconChartBar,
  IconDashboard,
  IconDatabase,
  IconFileAi,
  IconFileDescription,
  IconFileWord,
  IconFolder,
  IconHelp,
  IconInnerShadowTop,
  IconListDetails,
  IconReport,
  IconSearch,
  IconSettings,
  IconUsers,
  IconMapPin,
  IconStack,
  IconActivity,
  IconEye,
  IconTrendingUp,
  IconUsersGroup,
} from "@tabler/icons-react"

import { NavDocuments } from "@/components/nav-documents"
import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "仪表板",
      url: "/dashboard",
      icon: IconDashboard,
    },
    {
      title: "地图视图",
      url: "/map-view",
      icon: IconMapPin,
    },
    {
      title: "数据管理",
      url: "/data-management",
      icon: IconDatabase,
    },
    {
      title: "图层控制",
      url: "/layer-control",
      icon: IconStack,
    },
    {
      title: "空间分析",
      url: "/spatial-analysis",
      icon: IconActivity,
    },
    {
      title: "项目管理",
      url: "/projects",
      icon: IconFolder,
    },
    {
      title: "可视化",
      url: "/visualization",
      icon: IconEye,
    },
    {
      title: "报表中心",
      url: "/reports",
      icon: IconTrendingUp,
    },
    {
      title: "团队协作",
      url: "/team",
      icon: IconUsersGroup,
    },
    {
      title: "模型测试",
      url: "/model-test",
      icon: IconInnerShadowTop,
    },
  ],
  navClouds: [
    {
      title: "Capture",
      icon: IconCamera,
      isActive: true,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Proposal",
      icon: IconFileDescription,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
    {
      title: "Prompts",
      icon: IconFileAi,
      url: "#",
      items: [
        {
          title: "Active Proposals",
          url: "#",
        },
        {
          title: "Archived",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "系统设置",
      url: "/settings",
      icon: IconSettings,
    },
    {
      title: "帮助中心",
      url: "/help",
      icon: IconHelp,
    },
    {
      title: "搜索",
      url: "/search",
      icon: IconSearch,
    },
  ],
  documents: [
    {
      name: "数据字典",
      url: "/data-dictionary",
      icon: IconDatabase,
    },
    {
      name: "分析报告",
      url: "/analysis-reports",
      icon: IconReport,
    },
    {
      name: "地图图集",
      url: "/map-atlas",
      icon: IconMapPin,
    },
    {
      name: "操作手册",
      url: "/manual",
      icon: IconFileDescription,
    },
    {
      name: "API文档",
      url: "/api-docs",
      icon: IconFileWord,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar variant="inset" collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="#">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">Acme Inc.</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavDocuments items={data.documents} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}

import type { ReactNode } from "react"
import { useEffect, useState } from "react"

interface PageTransitionProps {
  children: ReactNode
  className?: string
}

export function PageTransition({ children, className = "" }: PageTransitionProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  return (
    <div 
      className={`transition-all duration-300 ease-out ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      } ${className}`}
    >
      {children}
    </div>
  )
}

export function FadeIn({ children, delay = 0, className = "" }: { 
  children: ReactNode
  delay?: number
  className?: string 
}) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay)
    return () => clearTimeout(timer)
  }, [delay])

  return (
    <div 
      className={`transition-opacity duration-500 ease-out ${
        isVisible ? 'opacity-100' : 'opacity-0'
      } ${className}`}
    >
      {children}
    </div>
  )
}

export function SlideIn({ children, direction = 'left', delay = 0, className = "" }: { 
  children: ReactNode
  direction?: 'left' | 'right' | 'up' | 'down'
  delay?: number
  className?: string 
}) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay)
    return () => clearTimeout(timer)
  }, [delay])

  const getTransformClass = () => {
    if (!isVisible) {
      switch (direction) {
        case 'left': return 'translate-x-[-20px]'
        case 'right': return 'translate-x-[20px]'
        case 'up': return 'translate-y-[-20px]'
        case 'down': return 'translate-y-[20px]'
      }
    }
    return 'translate-x-0 translate-y-0'
  }

  return (
    <div 
      className={`transition-all duration-500 ease-out ${
        isVisible ? 'opacity-100' : 'opacity-0'
      } ${getTransformClass()} ${className}`}
    >
      {children}
    </div>
  )
}

export function ScaleIn({ children, delay = 0, className = "" }: { 
  children: ReactNode
  delay?: number
  className?: string 
}) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay)
    return () => clearTimeout(timer)
  }, [delay])

  return (
    <div 
      className={`transition-all duration-300 ease-out ${
        isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
      } ${className}`}
    >
      {children}
    </div>
  )
}

export function StaggerChildren({ children, className = "" }: {
  children: ReactNode
  className?: string
}) {
  return (
    <div className={`space-y-4 ${className}`}>
      {children}
    </div>
  )
}
@import "tailwindcss";
@import "./styles/theme.css";
@import "tw-animate-css";

@custom-variant dark (&:where(.dark, .dark *));

@layer base {

  * {
    border-color: hsl(var(--color-border));
  }

  body {
    background-color: hsl(var(--color-background));
    color: hsl(var(--color-foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--color-muted));
    border-radius: 5px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--color-muted-foreground) / 0.3);
    border-radius: 5px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--color-muted-foreground) / 0.5);
  }

  /* Focus styles */
  *:focus-visible {
    outline: 2px solid hsl(var(--color-ring));
    outline-offset: 2px;
  }

  /* Selection styles */
  ::selection {
    background-color: hsl(var(--color-primary) / 0.2);
    color: hsl(var(--color-primary));
  }
}

@layer utilities {
  /* Animations */
  @keyframes slide-in {
    from {
      transform: translateY(10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes scale-in {
    from {
      transform: scale(0.95);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  .animate-slide-in {
    animation: slide-in 0.3s ease-out;
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.2s ease-out;
  }

  /* Glass morphism effect */
  .glass {
    background: hsl(var(--color-background) / 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid hsl(var(--color-border) / 0.5);
  }

  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, 
      hsl(var(--color-primary)) 0%, 
      hsl(var(--color-map-secondary)) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Shadow utilities */
  .shadow-soft {
    box-shadow: 
      0 1px 3px 0 rgb(0 0 0 / 0.1),
      0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .shadow-glow {
    box-shadow: 
      0 0 20px -5px hsl(var(--color-primary) / 0.3),
      0 0 40px -10px hsl(var(--color-primary) / 0.2);
  }

  /* Hover effects */
  .hover-lift {
    transition: transform 0.2s ease;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
  }

  /* Loading skeleton */
  @keyframes skeleton-loading {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .skeleton {
    background: linear-gradient(
      90deg,
      hsl(var(--color-muted)) 25%,
      hsl(var(--color-muted) / 0.5) 50%,
      hsl(var(--color-muted)) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
  }
}

/* BIM component utilities */
@utility bim-loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255,255,255,0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Custom component styles */
@layer components {
  /* Card hover effects */
  .card-hover {
    transition: all 0.3s ease;
  }

  .card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 
      0 10px 25px -5px rgb(0 0 0 / 0.1),
      0 8px 10px -6px rgb(0 0 0 / 0.1);
  }

  /* Button press effect */
  .btn-press {
    transition: all 0.1s ease;
  }

  .btn-press:active {
    transform: scale(0.98);
  }

  /* Input focus effects */
  .input-focus {
    transition: all 0.2s ease;
  }

  .input-focus:focus {
    border-color: hsl(var(--color-primary));
    box-shadow: 0 0 0 3px hsl(var(--color-primary) / 0.1);
  }
}

:root {
  --sidebar: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

.dark {
  --sidebar: hsl(240 5.9% 10%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(224.3 76.3% 48%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

@theme inline {
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
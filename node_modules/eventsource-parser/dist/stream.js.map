{"version": 3, "file": "stream.js", "sources": ["../src/stream.ts"], "sourcesContent": ["import {createParser} from './parse.ts'\nimport type {EventSourceMessage, EventSourceParser} from './types.ts'\n\n/**\n * Options for the EventSourceParserStream.\n *\n * @public\n */\nexport interface StreamOptions {\n  /**\n   * Behavior when a parsing error occurs.\n   *\n   * - A custom function can be provided to handle the error.\n   * - `'terminate'` will error the stream and stop parsing.\n   * - Any other value will ignore the error and continue parsing.\n   *\n   * @defaultValue `undefined`\n   */\n  onError?: 'terminate' | ((error: Error) => void)\n\n  /**\n   * Callback for when a reconnection interval is sent from the server.\n   *\n   * @param retry - The number of milliseconds to wait before reconnecting.\n   */\n  onRetry?: (retry: number) => void\n\n  /**\n   * Callback for when a comment is encountered in the stream.\n   *\n   * @param comment - The comment encountered in the stream.\n   */\n  onComment?: (comment: string) => void\n}\n\n/**\n * A TransformStream that ingests a stream of strings and produces a stream of `EventSourceMessage`.\n *\n * @example Basic usage\n * ```\n * const eventStream =\n *   response.body\n *     .pipeThrough(new TextDecoderStream())\n *     .pipeThrough(new EventSourceParserStream())\n * ```\n *\n * @example Terminate stream on parsing errors\n * ```\n * const eventStream =\n *  response.body\n *   .pipeThrough(new TextDecoderStream())\n *   .pipeThrough(new EventSourceParserStream({terminateOnError: true}))\n * ```\n *\n * @public\n */\nexport class EventSourceParserStream extends TransformStream<string, EventSourceMessage> {\n  constructor({onError, onRetry, onComment}: StreamOptions = {}) {\n    let parser!: EventSourceParser\n\n    super({\n      start(controller) {\n        parser = createParser({\n          onEvent: (event) => {\n            controller.enqueue(event)\n          },\n          onError(error) {\n            if (onError === 'terminate') {\n              controller.error(error)\n            } else if (typeof onError === 'function') {\n              onError(error)\n            }\n\n            // Ignore by default\n          },\n          onRetry,\n          onComment,\n        })\n      },\n      transform(chunk) {\n        parser.feed(chunk)\n      },\n    })\n  }\n}\n\nexport {type ErrorType, ParseError} from './errors.ts'\nexport type {EventSourceMessage} from './types.ts'\n"], "names": [], "mappings": ";;AAwDO,MAAM,gCAAgC,gBAA4C;AAAA,EACvF,YAAY,EAAC,SAAS,SAAS,UAAS,IAAmB,CAAA,GAAI;AACzD,QAAA;AAEE,UAAA;AAAA,MACJ,MAAM,YAAY;AAChB,iBAAS,aAAa;AAAA,UACpB,SAAS,CAAC,UAAU;AAClB,uBAAW,QAAQ,KAAK;AAAA,UAC1B;AAAA,UACA,QAAQ,OAAO;AACT,wBAAY,cACd,WAAW,MAAM,KAAK,IACb,OAAO,WAAY,cAC5B,QAAQ,KAAK;AAAA,UAIjB;AAAA,UACA;AAAA,UACA;AAAA,QAAA,CACD;AAAA,MACH;AAAA,MACA,UAAU,OAAO;AACf,eAAO,KAAK,KAAK;AAAA,MAAA;AAAA,IACnB,CACD;AAAA,EAAA;AAEL;"}
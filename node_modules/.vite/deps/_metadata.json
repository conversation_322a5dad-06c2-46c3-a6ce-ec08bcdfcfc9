{"hash": "974df255", "configHash": "c376f4c8", "lockfileHash": "4d90379a", "browserHash": "fbc47763", "optimized": {"@tanstack/react-table": {"src": "../../@tanstack/react-table/build/lib/index.mjs", "file": "@tanstack_react-table.js", "fileHash": "b5cd012e", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "046e75d5", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "b2eb9357", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "67f8b3f2", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "8c4e8909", "needsInterop": true}, "@dnd-kit/core": {"src": "../../@dnd-kit/core/dist/core.esm.js", "file": "@dnd-kit_core.js", "fileHash": "30213dc1", "needsInterop": false}, "@dnd-kit/modifiers": {"src": "../../@dnd-kit/modifiers/dist/modifiers.esm.js", "file": "@dnd-kit_modifiers.js", "fileHash": "6543ca1a", "needsInterop": false}, "@dnd-kit/sortable": {"src": "../../@dnd-kit/sortable/dist/sortable.esm.js", "file": "@dnd-kit_sortable.js", "fileHash": "9bf25aa8", "needsInterop": false}, "@dnd-kit/utilities": {"src": "../../@dnd-kit/utilities/dist/utilities.esm.js", "file": "@dnd-kit_utilities.js", "fileHash": "b5ceec8a", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "f679b50a", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "5117ceda", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "54cc0a22", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "4eb61619", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "dd77e532", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "be5fe24a", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "95e0ec79", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "3f38aed7", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "6ae48f36", "needsInterop": false}, "@radix-ui/react-toggle": {"src": "../../@radix-ui/react-toggle/dist/index.mjs", "file": "@radix-ui_react-toggle.js", "fileHash": "430d2005", "needsInterop": false}, "@radix-ui/react-toggle-group": {"src": "../../@radix-ui/react-toggle-group/dist/index.mjs", "file": "@radix-ui_react-toggle-group.js", "fileHash": "0659e89c", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "310617b8", "needsInterop": false}, "@tabler/icons-react": {"src": "../../@tabler/icons-react/dist/esm/tabler-icons-react.mjs", "file": "@tabler_icons-react.js", "fileHash": "fef2a66c", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "91522b96", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "eaea46cf", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "abbd9d39", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "38437ec2", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "24a3d828", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "86966a40", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "48974873", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "cde2f0b5", "needsInterop": false}, "vaul": {"src": "../../vaul/dist/index.mjs", "file": "vaul.js", "fileHash": "b17ceab4", "needsInterop": false}, "zod": {"src": "../../zod/index.js", "file": "zod.js", "fileHash": "fdbe57b4", "needsInterop": false}}, "chunks": {"chunk-INOPUDWX": {"file": "chunk-INOPUDWX.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-PUYPTISX": {"file": "chunk-PUYPTISX.js"}, "chunk-65ULZJWD": {"file": "chunk-65ULZJWD.js"}, "chunk-V5FHPIN4": {"file": "chunk-V5FHPIN4.js"}, "chunk-CM3PSKWJ": {"file": "chunk-CM3PSKWJ.js"}, "chunk-7JOD6VFA": {"file": "chunk-7JOD6VFA.js"}, "chunk-54SEFC7G": {"file": "chunk-54SEFC7G.js"}, "chunk-ZUFT65JS": {"file": "chunk-ZUFT65JS.js"}, "chunk-TKIXVAK7": {"file": "chunk-TKIXVAK7.js"}, "chunk-ACDCXWOL": {"file": "chunk-ACDCXWOL.js"}, "chunk-L45BHIW3": {"file": "chunk-L45BHIW3.js"}, "chunk-AP2TDAYG": {"file": "chunk-AP2TDAYG.js"}, "chunk-UV4WXWXQ": {"file": "chunk-UV4WXWXQ.js"}, "chunk-2ZH4O272": {"file": "chunk-2ZH4O272.js"}, "chunk-FWS3VN4O": {"file": "chunk-FWS3VN4O.js"}, "chunk-YPOGURNC": {"file": "chunk-YPOGURNC.js"}, "chunk-UT65MXNU": {"file": "chunk-UT65MXNU.js"}, "chunk-ZAW4BQMP": {"file": "chunk-ZAW4BQMP.js"}, "chunk-MJNCUEZK": {"file": "chunk-MJNCUEZK.js"}, "chunk-YEXTT7AB": {"file": "chunk-YEXTT7AB.js"}, "chunk-HE4GKDYE": {"file": "chunk-HE4GKDYE.js"}, "chunk-SWGRY3PF": {"file": "chunk-SWGRY3PF.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}
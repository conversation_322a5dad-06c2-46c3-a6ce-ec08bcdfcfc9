{"root": ["../../src/app.tsx", "../../src/testbutton.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/app-sidebar.tsx", "../../src/components/chart-area-interactive.tsx", "../../src/components/data-table.tsx", "../../src/components/index.ts", "../../src/components/nav-documents.tsx", "../../src/components/nav-main.tsx", "../../src/components/nav-secondary.tsx", "../../src/components/nav-user.tsx", "../../src/components/section-cards.tsx", "../../src/components/site-header.tsx", "../../src/components/appsidebar/appsidebar.tsx", "../../src/components/appsidebar/index.ts", "../../src/components/bimviewer/bimviewer.tsx", "../../src/components/bimviewer/index.ts", "../../src/components/bimvieweradvanced/bimvieweradvanced.tsx", "../../src/components/bimvieweradvanced/index.ts", "../../src/components/chartareainteractive/chartareainteractive.tsx", "../../src/components/chartareainteractive/index.ts", "../../src/components/datatable/datatable.tsx", "../../src/components/datatable/index.ts", "../../src/components/mobilenav/mobilenav.tsx", "../../src/components/mobilenav/index.ts", "../../src/components/navdocuments/navdocuments.tsx", "../../src/components/navdocuments/index.ts", "../../src/components/navmain/navmain.tsx", "../../src/components/navmain/index.ts", "../../src/components/navprojects/navprojects.tsx", "../../src/components/navprojects/index.ts", "../../src/components/navsecondary/navsecondary.tsx", "../../src/components/navsecondary/index.ts", "../../src/components/navuser/navuser.tsx", "../../src/components/navuser/index.ts", "../../src/components/pagetransition/pagetransition.tsx", "../../src/components/pagetransition/index.ts", "../../src/components/sectioncards/sectioncards.tsx", "../../src/components/sectioncards/index.ts", "../../src/components/siteheader/siteheader.tsx", "../../src/components/siteheader/index.ts", "../../src/components/teamswitcher/teamswitcher.tsx", "../../src/components/teamswitcher/index.ts", "../../src/components/ui/avatar.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/breadcrumb.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/chart.tsx", "../../src/components/ui/checkbox.tsx", "../../src/components/ui/collapsible.tsx", "../../src/components/ui/drawer.tsx", "../../src/components/ui/dropdown-menu.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/separator.tsx", "../../src/components/ui/sheet.tsx", "../../src/components/ui/sidebar.tsx", "../../src/components/ui/skeleton.tsx", "../../src/components/ui/sonner.tsx", "../../src/components/ui/table.tsx", "../../src/components/ui/tabs.tsx", "../../src/components/ui/toggle-group.tsx", "../../src/components/ui/toggle.tsx", "../../src/components/ui/tooltip.tsx", "../../src/hooks/use-mobile.ts", "../../src/lib/utils.ts", "../../src/pages/addextrude.tsx", "../../src/pages/bimviewerdemo.tsx", "../../src/pages/dashboard.tsx", "../../src/pages/dashboard01.tsx", "../../src/pages/home.tsx", "../../src/pages/notfound.tsx", "../../src/store/navigation.ts", "../../src/types/bim-air.d.ts", "../../src/types/navigation.ts"], "errors": true, "version": "5.8.3"}
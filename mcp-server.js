#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

/**
 * ForgeCode MCP Server
 * Provides tools and resources for the GIS examples project
 */

class ForgeCodeMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'forgecode-mcp',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
          resources: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupResourceHandlers();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'get_project_info',
            description: 'Get information about the current GIS project',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
          {
            name: 'analyze_gis_data',
            description: 'Analyze GIS data structures and formats',
            inputSchema: {
              type: 'object',
              properties: {
                dataType: {
                  type: 'string',
                  description: 'Type of GIS data to analyze (geojson, shapefile, etc.)',
                },
              },
              required: ['dataType'],
            },
          },
          {
            name: 'generate_map_component',
            description: 'Generate React components for map visualization',
            inputSchema: {
              type: 'object',
              properties: {
                mapType: {
                  type: 'string',
                  description: 'Type of map component (leaflet, mapbox, etc.)',
                },
                features: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'List of features to include in the map',
                },
              },
              required: ['mapType'],
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case 'get_project_info':
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify({
                  name: 'GIS Examples Project',
                  type: 'React + TypeScript + Vite',
                  description: 'A collection of GIS examples and visualizations',
                  technologies: ['React', 'TypeScript', 'Vite', 'Tailwind CSS', 'gs-bim-air'],
                  features: ['Interactive maps', 'Data visualization', 'Component library'],
                }, null, 2),
              },
            ],
          };

        case 'analyze_gis_data':
          const { dataType } = args;
          return {
            content: [
              {
                type: 'text',
                text: `Analyzing ${dataType} data format:\n\n` +
                      `Supported operations:\n` +
                      `- Data parsing and validation\n` +
                      `- Coordinate system transformations\n` +
                      `- Spatial analysis\n` +
                      `- Visualization preparation\n\n` +
                      `Recommended libraries:\n` +
                      `- Leaflet for interactive maps\n` +
                      `- Turf.js for spatial analysis\n` +
                      `- Proj4js for coordinate transformations`,
              },
            ],
          };

        case 'generate_map_component':
          const { mapType, features = [] } = args;
          const componentCode = this.generateMapComponent(mapType, features);
          return {
            content: [
              {
                type: 'text',
                text: componentCode,
              },
            ],
          };

        default:
          throw new Error(`Unknown tool: ${name}`);
      }
    });
  }

  setupResourceHandlers() {
    // Resource handlers can be added here for providing project files, documentation, etc.
  }

  generateMapComponent(mapType, features) {
    const baseComponent = `
import React, { useEffect, useRef } from 'react';

interface ${mapType.charAt(0).toUpperCase() + mapType.slice(1)}MapProps {
  width?: string;
  height?: string;
  center?: [number, number];
  zoom?: number;
}

export const ${mapType.charAt(0).toUpperCase() + mapType.slice(1)}Map: React.FC<${mapType.charAt(0).toUpperCase() + mapType.slice(1)}MapProps> = ({
  width = '100%',
  height = '400px',
  center = [0, 0],
  zoom = 2
}) => {
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Initialize ${mapType} map
    if (mapRef.current) {
      // Map initialization code here
      console.log('Initializing ${mapType} map with features:', ${JSON.stringify(features)});
    }
  }, []);

  return (
    <div 
      ref={mapRef} 
      style={{ width, height }}
      className="border rounded-lg shadow-md"
    />
  );
};
`;

    return `Generated ${mapType} map component with features: ${features.join(', ')}\n\n${baseComponent}`;
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('ForgeCode MCP Server running on stdio');
  }
}

// Start the server
const server = new ForgeCodeMCPServer();
server.run().catch(console.error);